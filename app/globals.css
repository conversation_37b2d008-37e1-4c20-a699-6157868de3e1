@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://rsms.me/inter/inter.css');

/* Design system tokens */
@import '../src/styles/design-tokens.css';

/* Gaming theme styles */
@import '../src/styles/gaming-theme.css';

/* Header hover effects for enhanced navigation experience */
@import '../src/styles/header-hover-effects.css';

/* Page transitions for smooth navigation */
@import '../src/styles/transitions.css';

/* Enhanced community navigation styles */
@import '../src/styles/community-navigation.css';

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 17, 24, 39;
  --background-end-rgb: 17, 24, 39;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom utility classes */
@layer components {
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 rounded-lg font-medium transition-all duration-200 ease-in-out;
  }

  .btn-primary {
    @apply btn bg-accent-600 hover:bg-accent-700 text-white;
  }

  .btn-secondary {
    @apply btn bg-gray-800 hover:bg-gray-700 text-white;
  }

  .btn-outline {
    @apply btn border border-white/20 hover:border-white/40 text-white;
  }

  .heading-xl {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold leading-tight;
  }

  .heading-lg {
    @apply text-3xl md:text-4xl font-bold leading-tight;
  }

  .heading-md {
    @apply text-2xl md:text-3xl font-bold leading-tight;
  }

  .heading-sm {
    @apply text-xl md:text-2xl font-bold leading-tight;
  }
}

/* Z-Index utility classes */
@layer utilities {
  .z-dropdown { z-index: var(--z-dropdown); }
  .z-sticky { z-index: var(--z-sticky); }
  .z-fixed { z-index: var(--z-fixed); }
  .z-modal-backdrop { z-index: var(--z-modal-backdrop); }
  .z-modal { z-index: var(--z-modal); }
  .z-popover { z-index: var(--z-popover); }
  .z-tooltip { z-index: var(--z-tooltip); }
  .z-toast { z-index: var(--z-toast); }

  /* Mobile-first responsive utilities */
  .mobile-container {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .mobile-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4;
  }

  .mobile-text {
    @apply text-sm sm:text-base lg:text-lg;
  }

  .mobile-spacing {
    @apply space-y-4 sm:space-y-6 lg:space-y-8;
  }

  /* Touch target utilities */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    @apply flex items-center justify-center;
  }

  .touch-target-sm {
    min-height: 40px;
    min-width: 40px;
    @apply flex items-center justify-center;
  }
}

/* Custom color variables for accent */
:root {
  --accent-50: #fef7ee;
  --accent-100: #fdedd3;
  --accent-200: #fbd7a5;
  --accent-300: #f8bb6d;
  --accent-400: #f59e0b;
  --accent-500: #d97706;
  --accent-600: #c2410c;
  --accent-700: #9a3412;
  --accent-800: #7c2d12;
  --accent-900: #431407;

  /* Standardized Z-Index Scale for Syndicaps */
  --z-base: 1;
  --z-dropdown: 1000;
  --z-sticky: 1010;
  --z-fixed: 1020;
  --z-modal-backdrop: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
  --z-toast: 1070;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Hero Raffle Button Styles */
.hero-raffle-button button {
  @apply px-4 py-2 text-sm;
}

.hero-raffle-button .space-y-3 {
  @apply space-y-2;
}

.hero-raffle-button p {
  @apply text-xs;
}
